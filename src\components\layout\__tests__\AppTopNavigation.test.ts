import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import AppTopNavigation from '../AppTopNavigation.vue'
import { useAuthStore } from '@/stores/authStore'
import type { User } from '@/types'

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/info', component: { template: '<div>Info</div>' } },
    { path: '/study', component: { template: '<div>Study</div>' } },
    { path: '/exam', component: { template: '<div>Exam</div>' } },
    { path: '/profile', component: { template: '<div>Profile</div>' } },
    { path: '/login', component: { template: '<div>Login</div>' } },
  ],
})

const mockUser: User = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  name: '测试用户',
  realName: '张三',
  avatar: 'https://example.com/avatar.jpg',
  permissions: ['info:read', 'study:read', 'exam:read', 'profile:read'],
  roles: ['user'],
  position: '医师',
}

describe('AppTopNavigation', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  const createWrapper = async (route = '/info') => {
    await router.push(route)
    return mount(AppTopNavigation, {
      global: {
        plugins: [router],
      },
    })
  }

  describe('未登录状态', () => {
    it('应该显示登录按钮', async () => {
      const wrapper = await createWrapper()
      
      expect(wrapper.find('a[href="/login"]').exists()).toBe(true)
      expect(wrapper.text()).toContain('登录')
    })

    it('应该显示所有导航菜单项', async () => {
      const wrapper = await createWrapper()
      
      expect(wrapper.text()).toContain('信息中心')
      expect(wrapper.text()).toContain('学习中心')
      expect(wrapper.text()).toContain('考试中心')
      expect(wrapper.text()).toContain('个人中心')
    })

    it('应该显示CDC logo和系统名称', async () => {
      const wrapper = await createWrapper()
      
      expect(wrapper.text()).toContain('CDC')
      expect(wrapper.text()).toContain('疾控考试系统')
    })
  })

  describe('已登录状态', () => {
    beforeEach(() => {
      const authStore = useAuthStore()
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })
    })

    it('应该显示用户信息', async () => {
      const wrapper = await createWrapper()
      
      expect(wrapper.text()).toContain('张三')
      expect(wrapper.find('a[href="/login"]').exists()).toBe(false)
    })

    it('应该显示用户头像', async () => {
      const wrapper = await createWrapper()
      
      const avatar = wrapper.find('[class*="bg-gradient-to-br"]')
      expect(avatar.exists()).toBe(true)
      expect(avatar.text()).toContain('张')
    })

    it('点击用户菜单应该显示下拉菜单', async () => {
      const wrapper = await createWrapper()
      
      const userButton = wrapper.find('button[class*="flex items-center space-x-3"]')
      await userButton.trigger('click')
      
      expect(wrapper.text()).toContain('个人中心')
      expect(wrapper.text()).toContain('退出登录')
    })

    it('点击退出登录应该调用logout', async () => {
      const authStore = useAuthStore()
      const logoutSpy = vi.spyOn(authStore, 'logout')
      
      const wrapper = await createWrapper()
      
      // 打开用户菜单
      const userButton = wrapper.find('button[class*="flex items-center space-x-3"]')
      await userButton.trigger('click')
      
      // 点击退出登录
      const logoutButton = wrapper.find('button:contains("退出登录")')
      if (logoutButton.exists()) {
        await logoutButton.trigger('click')
        expect(logoutSpy).toHaveBeenCalled()
      }
    })
  })

  describe('导航功能', () => {
    it('应该正确高亮当前路由', async () => {
      const wrapper = await createWrapper('/info')
      
      const infoButton = wrapper.find('button:contains("信息中心")')
      expect(infoButton.classes()).toContain('bg-blue-600')
    })

    it('点击导航按钮应该跳转到对应路由', async () => {
      const wrapper = await createWrapper('/info')
      
      const studyButton = wrapper.find('button:contains("学习中心")')
      await studyButton.trigger('click')
      
      expect(router.currentRoute.value.path).toBe('/study')
    })

    it('Logo点击应该跳转到信息中心', async () => {
      const wrapper = await createWrapper('/study')
      
      const logoLink = wrapper.find('a[href="/info"]')
      await logoLink.trigger('click')
      
      expect(router.currentRoute.value.path).toBe('/info')
    })
  })

  describe('响应式设计', () => {
    it('应该在移动端显示菜单按钮', async () => {
      const wrapper = await createWrapper()
      
      const mobileMenuButton = wrapper.find('button[class*="md:hidden"]')
      expect(mobileMenuButton.exists()).toBe(true)
    })

    it('点击移动端菜单按钮应该切换菜单显示状态', async () => {
      const wrapper = await createWrapper()
      
      const mobileMenuButton = wrapper.find('button[class*="md:hidden"]')
      await mobileMenuButton.trigger('click')
      
      // 检查移动端菜单是否显示
      const mobileMenu = wrapper.find('[class*="md:hidden border-t"]')
      expect(mobileMenu.exists()).toBe(true)
    })
  })

  describe('外部点击关闭菜单', () => {
    it('点击遮罩应该关闭用户菜单', async () => {
      const authStore = useAuthStore()
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })
      
      const wrapper = await createWrapper()
      
      // 打开用户菜单
      const userButton = wrapper.find('button[class*="flex items-center space-x-3"]')
      await userButton.trigger('click')
      
      // 点击遮罩
      const overlay = wrapper.find('[class*="fixed inset-0"]')
      if (overlay.exists()) {
        await overlay.trigger('click')
        
        // 菜单应该关闭
        await wrapper.vm.$nextTick()
        expect(wrapper.find('[class*="absolute right-0 mt-2"]').exists()).toBe(false)
      }
    })
  })
})
