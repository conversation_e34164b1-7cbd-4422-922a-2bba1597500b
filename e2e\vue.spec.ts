import { test, expect } from '@playwright/test'

// 测试应用根路径重定向到信息中心
test('visits the app root url and redirects to info center', async ({ page }) => {
  await page.goto('/')

  // 等待重定向完成
  await page.waitForURL('/info')

  // 验证页面标题包含"信息中心"
  await expect(page.locator('h1')).toContainText('信息中心')

  // 验证导航栏存在
  await expect(page.locator('header')).toBeVisible()

  // 验证CDC logo存在
  await expect(page.locator('text=CDC')).toBeVisible()
})
