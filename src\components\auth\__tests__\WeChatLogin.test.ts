import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import WeChatLogin from '../WeChatLogin.vue'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'

// Mock timers
vi.useFakeTimers()

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/info', component: { template: '<div>Info</div>' } },
    { path: '/login', component: { template: '<div>Login</div>' } },
  ],
})

// Mock AppButton component
vi.mock('@/components/ui/AppButton.vue', () => ({
  default: {
    name: 'AppButton',
    template: '<button><slot /></button>',
    props: ['variant', 'size', 'disabled'],
  },
}))

describe('WeChatLogin', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    vi.clearAllTimers()
  })

  afterEach(() => {
    vi.runOnlyPendingTimers()
    vi.useRealTimers()
    vi.useFakeTimers()
  })

  const createWrapper = (props = {}) => {
    return mount(WeChatLogin, {
      props,
      global: {
        plugins: [router],
        stubs: {
          AppButton: true,
        },
      },
    })
  }

  describe('初始状态', () => {
    it('应该正确渲染初始状态', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('微信扫码登录')
      expect(wrapper.text()).toContain('请使用微信扫描下方二维码登录')
    })

    it('组件挂载时应该自动生成二维码', async () => {
      const wrapper = createWrapper()
      
      // 等待异步操作完成
      await vi.runAllTimersAsync()
      
      expect(wrapper.vm.qrCodeUrl).toBeTruthy()
      expect(wrapper.vm.scanStatus).toBe('waiting')
    })

    it('应该显示加载状态', () => {
      const wrapper = createWrapper()
      
      // 在二维码生成过程中应该显示加载状态
      expect(wrapper.vm.isLoading).toBe(true)
    })
  })

  describe('二维码生成', () => {
    it('生成二维码后应该显示二维码图片', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      const qrImage = wrapper.find('img[alt="微信登录二维码"]')
      expect(qrImage.exists()).toBe(true)
      expect(qrImage.attributes('src')).toContain('qrcode')
    })

    it('应该开始轮询扫码状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      expect(wrapper.vm.isPolling).toBe(true)
    })

    it('二维码应该在5分钟后过期', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 快进5分钟
      vi.advanceTimersByTime(5 * 60 * 1000)
      
      expect(wrapper.vm.qrCodeExpired).toBe(true)
      expect(wrapper.vm.scanStatus).toBe('expired')
    })
  })

  describe('扫码状态', () => {
    it('应该正确显示等待扫码状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      expect(wrapper.text()).toContain('请使用微信扫描二维码')
      expect(wrapper.find('[class*="text-gray-600"]').exists()).toBe(true)
    })

    it('应该正确显示已扫码状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 模拟扫码状态变化
      await wrapper.setData({ scanStatus: 'scanned' })
      
      expect(wrapper.text()).toContain('扫码成功，请在手机上确认登录')
    })

    it('应该正确显示确认登录状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 模拟确认状态
      await wrapper.setData({ scanStatus: 'confirmed' })
      
      expect(wrapper.text()).toContain('登录确认中...')
    })

    it('应该正确显示过期状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 模拟过期状态
      await wrapper.setData({ scanStatus: 'expired', qrCodeExpired: true })
      
      expect(wrapper.text()).toContain('二维码已过期')
    })
  })

  describe('刷新二维码', () => {
    it('点击刷新按钮应该重新生成二维码', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 设置为过期状态
      await wrapper.setData({ scanStatus: 'expired', qrCodeExpired: true })
      
      const refreshButton = wrapper.find('button:contains("刷新二维码")')
      if (refreshButton.exists()) {
        await refreshButton.trigger('click')
        
        expect(wrapper.vm.qrCodeExpired).toBe(false)
        expect(wrapper.vm.scanStatus).toBe('waiting')
      }
    })

    it('刷新时应该停止之前的轮询', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      const stopPollingSpy = vi.spyOn(wrapper.vm, 'stopPolling')
      
      // 触发刷新
      await wrapper.vm.generateQRCode()
      
      expect(stopPollingSpy).toHaveBeenCalled()
    })
  })

  describe('登录成功处理', () => {
    it('登录成功后应该调用onSuccess回调', async () => {
      const onSuccess = vi.fn()
      const wrapper = createWrapper({ onSuccess })
      
      await vi.runAllTimersAsync()
      
      // 模拟登录成功
      await wrapper.vm.handleLoginSuccess({
        token: 'mock-token',
        user: { id: '1', username: 'test' },
        expiresIn: 3600,
      })
      
      expect(onSuccess).toHaveBeenCalled()
    })

    it('登录成功后应该设置认证状态', async () => {
      const wrapper = createWrapper()
      const authStore = useAuthStore()
      const setAuthSpy = vi.spyOn(authStore, 'setAuth')
      
      await vi.runAllTimersAsync()
      
      const loginData = {
        token: 'mock-token',
        user: { id: '1', username: 'test' },
        expiresIn: 3600,
      }
      
      await wrapper.vm.handleLoginSuccess(loginData)
      
      expect(setAuthSpy).toHaveBeenCalledWith(loginData)
    })

    it('登录成功后应该显示成功通知', async () => {
      const wrapper = createWrapper()
      const notificationStore = useNotificationStore()
      const addNotificationSpy = vi.spyOn(notificationStore, 'addNotification')
      
      await vi.runAllTimersAsync()
      
      await wrapper.vm.handleLoginSuccess({
        token: 'mock-token',
        user: { id: '1', username: 'test' },
        expiresIn: 3600,
      })
      
      expect(addNotificationSpy).toHaveBeenCalledWith({
        type: 'success',
        title: '登录成功',
        message: '欢迎使用疾控考试系统',
      })
    })
  })

  describe('错误处理', () => {
    it('应该正确处理网络错误', async () => {
      const onError = vi.fn()
      const wrapper = createWrapper({ onError })
      
      // 模拟网络错误
      await wrapper.vm.handleError('网络连接失败')
      
      expect(onError).toHaveBeenCalledWith('网络连接失败')
    })

    it('错误时应该显示错误通知', async () => {
      const wrapper = createWrapper()
      const notificationStore = useNotificationStore()
      const addNotificationSpy = vi.spyOn(notificationStore, 'addNotification')
      
      await wrapper.vm.handleError('登录失败')
      
      expect(addNotificationSpy).toHaveBeenCalledWith({
        type: 'error',
        title: '登录失败',
        message: '登录失败',
      })
    })
  })

  describe('组件销毁', () => {
    it('组件销毁时应该清理轮询定时器', () => {
      const wrapper = createWrapper()
      const stopPollingSpy = vi.spyOn(wrapper.vm, 'stopPolling')
      
      wrapper.unmount()
      
      expect(stopPollingSpy).toHaveBeenCalled()
    })
  })

  describe('轮询机制', () => {
    it('应该定期检查扫码状态', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      expect(wrapper.vm.isPolling).toBe(true)
      
      // 快进轮询间隔
      vi.advanceTimersByTime(3000)
      
      // 验证轮询仍在进行
      expect(wrapper.vm.isPolling).toBe(true)
    })

    it('登录成功后应该停止轮询', async () => {
      const wrapper = createWrapper()
      
      await vi.runAllTimersAsync()
      
      // 模拟登录成功
      await wrapper.vm.handleLoginSuccess({
        token: 'mock-token',
        user: { id: '1', username: 'test' },
        expiresIn: 3600,
      })
      
      expect(wrapper.vm.isPolling).toBe(false)
    })
  })
})
