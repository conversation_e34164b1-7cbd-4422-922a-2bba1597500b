import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import AppLayout from '../AppLayout.vue'
import AppTopNavigation from '../AppTopNavigation.vue'
import AppFooter from '../AppFooter.vue'
import AppNotification from '@/components/ui/AppNotification.vue'
import { useAuthStore } from '@/stores/authStore'

// Mock components
vi.mock('../AppTopNavigation.vue', () => ({
  default: {
    name: 'AppTopNavigation',
    template: '<nav data-testid="top-navigation">Top Navigation</nav>',
  },
}))

vi.mock('../AppFooter.vue', () => ({
  default: {
    name: 'AppFooter',
    template: '<footer data-testid="footer">Footer</footer>',
  },
}))

vi.mock('@/components/ui/AppNotification.vue', () => ({
  default: {
    name: 'AppNotification',
    template: '<div data-testid="notification">Notification</div>',
  },
}))

// Mock router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/info', component: { template: '<div data-testid="router-view">Info Page</div>' } },
    { path: '/study', component: { template: '<div data-testid="router-view">Study Page</div>' } },
  ],
})

describe('AppLayout', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
  })

  const createWrapper = async (route = '/info') => {
    await router.push(route)
    return mount(AppLayout, {
      global: {
        plugins: [router],
        stubs: {
          AppTopNavigation: true,
          AppFooter: true,
          AppNotification: true,
        },
      },
    })
  }

  describe('布局结构', () => {
    it('应该渲染正确的布局结构', async () => {
      const wrapper = await createWrapper()
      
      // 检查主容器
      const mainContainer = wrapper.find('[class*="min-h-screen bg-gray-50 flex flex-col"]')
      expect(mainContainer.exists()).toBe(true)
      
      // 检查顶部导航
      expect(wrapper.findComponent({ name: 'AppTopNavigation' }).exists()).toBe(true)
      
      // 检查主内容区域
      const mainContent = wrapper.find('main[class*="flex-1"]')
      expect(mainContent.exists()).toBe(true)
      
      // 检查页脚
      expect(wrapper.findComponent({ name: 'AppFooter' }).exists()).toBe(true)
      
      // 检查通知组件
      expect(wrapper.findComponent({ name: 'AppNotification' }).exists()).toBe(true)
    })

    it('应该包含router-view用于显示页面内容', async () => {
      const wrapper = await createWrapper()
      
      const routerView = wrapper.findComponent({ name: 'RouterView' })
      expect(routerView.exists()).toBe(true)
    })

    it('主内容区域应该有正确的样式类', async () => {
      const wrapper = await createWrapper()
      
      const mainContent = wrapper.find('main')
      expect(mainContent.classes()).toContain('flex-1')
      
      const contentContainer = wrapper.find('[class*="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"]')
      expect(contentContainer.exists()).toBe(true)
    })
  })

  describe('认证状态初始化', () => {
    it('组件挂载时应该初始化认证状态', async () => {
      const authStore = useAuthStore()
      const initAuthSpy = vi.spyOn(authStore, 'initAuth')
      
      await createWrapper()
      
      expect(initAuthSpy).toHaveBeenCalled()
    })
  })

  describe('响应式设计', () => {
    it('应该有响应式的容器样式', async () => {
      const wrapper = await createWrapper()
      
      const container = wrapper.find('[class*="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"]')
      expect(container.exists()).toBe(true)
      
      // 检查响应式padding类
      expect(container.classes()).toContain('px-4')
      expect(container.classes()).toContain('sm:px-6')
      expect(container.classes()).toContain('lg:px-8')
    })

    it('主容器应该有最小高度和flex布局', async () => {
      const wrapper = await createWrapper()
      
      const mainContainer = wrapper.find('div').at(0)
      expect(mainContainer.classes()).toContain('min-h-screen')
      expect(mainContainer.classes()).toContain('flex')
      expect(mainContainer.classes()).toContain('flex-col')
    })
  })

  describe('组件集成', () => {
    it('所有子组件应该正确渲染', async () => {
      const wrapper = await createWrapper()
      
      // 验证所有主要组件都存在
      expect(wrapper.findComponent({ name: 'AppTopNavigation' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'AppFooter' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'AppNotification' }).exists()).toBe(true)
      expect(wrapper.findComponent({ name: 'RouterView' }).exists()).toBe(true)
    })

    it('组件应该按正确顺序排列', async () => {
      const wrapper = await createWrapper()
      
      const children = wrapper.find('[class*="min-h-screen"]').element.children
      
      // 第一个应该是顶部导航
      expect(children[0].tagName.toLowerCase()).toBe('nav')
      
      // 第二个应该是主内容区域
      expect(children[1].tagName.toLowerCase()).toBe('main')
      
      // 第三个应该是页脚
      expect(children[2].tagName.toLowerCase()).toBe('footer')
      
      // 第四个应该是通知组件
      expect(children[3].getAttribute('data-testid')).toBe('notification')
    })
  })

  describe('样式和主题', () => {
    it('应该使用正确的背景色', async () => {
      const wrapper = await createWrapper()
      
      const mainContainer = wrapper.find('div').at(0)
      expect(mainContainer.classes()).toContain('bg-gray-50')
    })

    it('主内容区域应该有正确的间距', async () => {
      const wrapper = await createWrapper()
      
      const contentContainer = wrapper.find('[class*="py-8"]')
      expect(contentContainer.exists()).toBe(true)
      expect(contentContainer.classes()).toContain('py-8')
    })
  })

  describe('路由集成', () => {
    it('应该正确显示不同路由的内容', async () => {
      // 测试信息页面
      const infoWrapper = await createWrapper('/info')
      expect(infoWrapper.html()).toContain('router-view')
      
      // 测试学习页面
      const studyWrapper = await createWrapper('/study')
      expect(studyWrapper.html()).toContain('router-view')
    })
  })
})
