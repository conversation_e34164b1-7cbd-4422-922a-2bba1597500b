import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia, setActivePinia } from 'pinia'
import { useAuthStore } from '@/stores/authStore'
import type { User } from '@/types'

// 创建测试路由器
function createTestRouter() {
  return createRouter({
    history: createWebHistory(),
    routes: [
      {
        path: '/',
        redirect: '/info',
      },
      {
        path: '/login',
        name: 'login',
        component: { template: '<div>Login</div>' },
        meta: { guestAllowed: true },
      },
      {
        path: '/info',
        name: 'info',
        component: { template: '<div>Info</div>' },
        meta: { guestAllowed: true },
      },
      {
        path: '/study',
        name: 'study',
        component: { template: '<div>Study</div>' },
        meta: { guestAllowed: true },
      },
      {
        path: '/exam',
        name: 'exam',
        component: { template: '<div>Exam</div>' },
        meta: { guestAllowed: true },
      },
      {
        path: '/exam/:id/take/:recordId',
        name: 'exam-take',
        component: { template: '<div>Exam Take</div>' },
        meta: { requiresAuth: true, permission: 'exam:take' },
      },
      {
        path: '/profile',
        name: 'profile',
        component: { template: '<div>Profile</div>' },
        meta: { guestAllowed: true },
      },
      {
        path: '/admin',
        name: 'admin',
        component: { template: '<div>Admin</div>' },
        meta: { requiresAuth: true, permission: 'admin:access' },
      },
    ],
  })
}

// 添加路由守卫
function addRouteGuards(router: any) {
  router.beforeEach((to: any, from: any, next: any) => {
    const authStore = useAuthStore()

    // 如果是访客允许的页面，直接通行
    if (to.meta.guestAllowed) {
      // 如果已登录用户访问登录页，重定向到首页
      if (to.name === 'login' && authStore.isAuthenticated) {
        // 处理登录后的重定向
        const redirect = to.query.redirect as string
        if (redirect && redirect !== '/login') {
          next(redirect)
        } else {
          next('/info')
        }
        return
      }
      next()
      return
    }

    // 需要认证的页面
    if (!authStore.isAuthenticated) {
      // 未登录，重定向到登录页，并保存当前路径用于登录后恢复
      next({ name: 'login', query: { redirect: to.fullPath } })
      return
    }

    // 检查权限
    if (to.meta.permission && !authStore.hasPermission(to.meta.permission as string)) {
      // 权限不足，重定向到信息中心
      next('/info')
      return
    }

    next()
  })
}

const mockUser: User = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  name: '测试用户',
  realName: '张三',
  avatar: 'https://example.com/avatar.jpg',
  permissions: ['exam:take', 'profile:read'],
  roles: ['user'],
  position: '医师',
}

describe('Router', () => {
  let router: any
  let authStore: any

  beforeEach(() => {
    setActivePinia(createPinia())
    authStore = useAuthStore()
    router = createTestRouter()
    addRouteGuards(router)
    vi.clearAllMocks()
  })

  describe('基本路由重定向', () => {
    it('根路径应该重定向到信息中心', async () => {
      await router.push('/')
      expect(router.currentRoute.value.path).toBe('/info')
    })

    it('应该正确解析信息中心路由', async () => {
      await router.push('/info')
      expect(router.currentRoute.value.name).toBe('info')
      expect(router.currentRoute.value.path).toBe('/info')
    })

    it('应该正确解析学习中心路由', async () => {
      await router.push('/study')
      expect(router.currentRoute.value.name).toBe('study')
      expect(router.currentRoute.value.path).toBe('/study')
    })

    it('应该正确解析考试中心路由', async () => {
      await router.push('/exam')
      expect(router.currentRoute.value.name).toBe('exam')
      expect(router.currentRoute.value.path).toBe('/exam')
    })
  })

  describe('访客允许的页面', () => {
    it('未登录用户可以访问信息中心', async () => {
      await router.push('/info')
      expect(router.currentRoute.value.path).toBe('/info')
    })

    it('未登录用户可以访问学习中心', async () => {
      await router.push('/study')
      expect(router.currentRoute.value.path).toBe('/study')
    })

    it('未登录用户可以访问考试中心', async () => {
      await router.push('/exam')
      expect(router.currentRoute.value.path).toBe('/exam')
    })

    it('未登录用户可以访问个人中心', async () => {
      await router.push('/profile')
      expect(router.currentRoute.value.path).toBe('/profile')
    })

    it('未登录用户可以访问登录页', async () => {
      await router.push('/login')
      expect(router.currentRoute.value.path).toBe('/login')
    })
  })

  describe('登录页面重定向逻辑', () => {
    it('已登录用户访问登录页应该重定向到信息中心', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/login')
      expect(router.currentRoute.value.path).toBe('/info')
    })

    it('已登录用户访问带redirect参数的登录页应该重定向到指定页面', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/login?redirect=/study')
      expect(router.currentRoute.value.path).toBe('/study')
    })

    it('已登录用户访问登录页且redirect为登录页本身应该重定向到信息中心', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/login?redirect=/login')
      expect(router.currentRoute.value.path).toBe('/info')
    })
  })

  describe('需要认证的页面', () => {
    it('未登录用户访问需要认证的页面应该重定向到登录页', async () => {
      await router.push('/exam/1/take/1')
      
      expect(router.currentRoute.value.name).toBe('login')
      expect(router.currentRoute.value.query.redirect).toBe('/exam/1/take/1')
    })

    it('已登录用户可以访问有权限的页面', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/exam/1/take/1')
      expect(router.currentRoute.value.path).toBe('/exam/1/take/1')
    })

    it('已登录用户访问无权限的页面应该重定向到信息中心', async () => {
      // 设置已登录状态（但没有admin权限）
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/admin')
      expect(router.currentRoute.value.path).toBe('/info')
    })
  })

  describe('权限检查', () => {
    it('应该正确检查用户权限', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: {
          ...mockUser,
          permissions: ['admin:access'],
        },
        expiresIn: 3600,
      })

      await router.push('/admin')
      expect(router.currentRoute.value.path).toBe('/admin')
    })

    it('没有权限的用户应该被重定向到信息中心', async () => {
      // 设置已登录状态但没有admin权限
      authStore.setAuth({
        token: 'mock-token',
        user: {
          ...mockUser,
          permissions: ['exam:take'],
        },
        expiresIn: 3600,
      })

      await router.push('/admin')
      expect(router.currentRoute.value.path).toBe('/info')
    })
  })

  describe('登录后重定向', () => {
    it('登录后应该重定向到之前尝试访问的页面', async () => {
      // 未登录时访问需要认证的页面
      await router.push('/exam/1/take/1')
      expect(router.currentRoute.value.name).toBe('login')
      expect(router.currentRoute.value.query.redirect).toBe('/exam/1/take/1')

      // 模拟登录
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      // 再次访问登录页（模拟登录成功后的重定向）
      await router.push('/login?redirect=/exam/1/take/1')
      expect(router.currentRoute.value.path).toBe('/exam/1/take/1')
    })

    it('登录后如果没有redirect参数应该重定向到信息中心', async () => {
      // 设置已登录状态
      authStore.setAuth({
        token: 'mock-token',
        user: mockUser,
        expiresIn: 3600,
      })

      await router.push('/login')
      expect(router.currentRoute.value.path).toBe('/info')
    })
  })

  describe('路由元信息', () => {
    it('访客允许的页面应该有正确的meta信息', () => {
      const infoRoute = router.getRoutes().find((route: any) => route.name === 'info')
      expect(infoRoute?.meta.guestAllowed).toBe(true)
    })

    it('需要认证的页面应该有正确的meta信息', () => {
      const examTakeRoute = router.getRoutes().find((route: any) => route.name === 'exam-take')
      expect(examTakeRoute?.meta.requiresAuth).toBe(true)
      expect(examTakeRoute?.meta.permission).toBe('exam:take')
    })

    it('管理页面应该有正确的权限要求', () => {
      const adminRoute = router.getRoutes().find((route: any) => route.name === 'admin')
      expect(adminRoute?.meta.requiresAuth).toBe(true)
      expect(adminRoute?.meta.permission).toBe('admin:access')
    })
  })
})
