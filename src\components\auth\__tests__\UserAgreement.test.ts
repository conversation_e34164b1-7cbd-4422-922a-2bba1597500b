import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import UserAgreement from '../UserAgreement.vue'

// Mock AppButton component
vi.mock('@/components/ui/AppButton.vue', () => ({
  default: {
    name: 'AppButton',
    template: '<button :disabled="disabled" :class="variant"><slot /></button>',
    props: ['variant', 'size', 'disabled'],
  },
}))

describe('UserAgreement', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const createWrapper = (props = {}) => {
    return mount(UserAgreement, {
      props: {
        modelValue: false,
        ...props,
      },
      global: {
        stubs: {
          AppButton: true,
        },
      },
    })
  }

  describe('基本渲染', () => {
    it('应该正确渲染协议勾选框', () => {
      const wrapper = createWrapper()
      
      const checkbox = wrapper.find('input[type="checkbox"]')
      expect(checkbox.exists()).toBe(true)
      expect(checkbox.element.checked).toBe(false)
    })

    it('应该显示协议文本和链接', () => {
      const wrapper = createWrapper()
      
      expect(wrapper.text()).toContain('我已阅读并同意')
      expect(wrapper.text()).toContain('用户服务协议')
      expect(wrapper.text()).toContain('隐私政策')
    })

    it('协议链接应该可以点击', () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      const privacyLink = wrapper.find('button:contains("隐私政策")')
      
      expect(serviceLink.exists()).toBe(true)
      expect(privacyLink.exists()).toBe(true)
    })
  })

  describe('勾选框交互', () => {
    it('点击勾选框应该切换状态', async () => {
      const wrapper = createWrapper()
      
      const checkbox = wrapper.find('input[type="checkbox"]')
      await checkbox.trigger('change')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([true])
    })

    it('应该正确显示勾选状态', () => {
      const wrapper = createWrapper({ modelValue: true })
      
      const checkbox = wrapper.find('input[type="checkbox"]')
      expect(checkbox.element.checked).toBe(true)
    })

    it('点击标签应该切换勾选状态', async () => {
      const wrapper = createWrapper()
      
      const label = wrapper.find('label')
      await label.trigger('click')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
    })
  })

  describe('协议弹窗', () => {
    it('点击用户服务协议链接应该打开弹窗', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      expect(wrapper.vm.showModal).toBe(true)
      
      // 检查弹窗内容
      const modal = wrapper.find('[class*="fixed inset-0"]')
      expect(modal.exists()).toBe(true)
    })

    it('点击隐私政策链接应该打开弹窗', async () => {
      const wrapper = createWrapper()
      
      const privacyLink = wrapper.find('button:contains("隐私政策")')
      await privacyLink.trigger('click')
      
      expect(wrapper.vm.showModal).toBe(true)
    })

    it('弹窗应该显示协议内容', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      expect(wrapper.text()).toContain('用户服务协议')
      expect(wrapper.text()).toContain('隐私政策')
      expect(wrapper.text()).toContain('服务条款')
    })

    it('弹窗应该有关闭按钮', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      const closeButton = wrapper.find('button[class*="text-gray-400"]')
      expect(closeButton.exists()).toBe(true)
    })
  })

  describe('弹窗操作', () => {
    it('点击同意按钮应该关闭弹窗并设置同意状态', async () => {
      const onAgree = vi.fn()
      const wrapper = createWrapper({ onAgree })
      
      // 打开弹窗
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      // 点击同意按钮
      const agreeButton = wrapper.find('button:contains("同意并继续")')
      await agreeButton.trigger('click')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([true])
      expect(onAgree).toHaveBeenCalled()
      expect(wrapper.vm.showModal).toBe(false)
    })

    it('点击不同意按钮应该关闭弹窗并设置不同意状态', async () => {
      const onDisagree = vi.fn()
      const wrapper = createWrapper({ onDisagree })
      
      // 打开弹窗
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      // 点击不同意按钮
      const disagreeButton = wrapper.find('button:contains("暂不同意")')
      await disagreeButton.trigger('click')
      
      expect(wrapper.emitted('update:modelValue')).toBeTruthy()
      expect(wrapper.emitted('update:modelValue')[0]).toEqual([false])
      expect(onDisagree).toHaveBeenCalled()
      expect(wrapper.vm.showModal).toBe(false)
    })

    it('点击关闭按钮应该关闭弹窗', async () => {
      const wrapper = createWrapper()
      
      // 打开弹窗
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      // 点击关闭按钮
      const closeButton = wrapper.find('button[class*="text-gray-400"]')
      await closeButton.trigger('click')
      
      expect(wrapper.vm.showModal).toBe(false)
    })

    it('点击遮罩应该关闭弹窗', async () => {
      const wrapper = createWrapper()
      
      // 打开弹窗
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      // 点击遮罩
      const overlay = wrapper.find('[class*="fixed inset-0 bg-black"]')
      await overlay.trigger('click')
      
      expect(wrapper.vm.showModal).toBe(false)
    })
  })

  describe('协议内容', () => {
    it('应该显示用户服务协议的详细内容', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      // 检查协议内容的关键部分
      expect(wrapper.text()).toContain('用户服务协议')
      expect(wrapper.text()).toContain('服务条款')
      expect(wrapper.text()).toContain('用户权利')
      expect(wrapper.text()).toContain('服务内容')
    })

    it('应该显示隐私政策的详细内容', async () => {
      const wrapper = createWrapper()
      
      const privacyLink = wrapper.find('button:contains("隐私政策")')
      await privacyLink.trigger('click')
      
      // 检查隐私政策内容的关键部分
      expect(wrapper.text()).toContain('隐私政策')
      expect(wrapper.text()).toContain('信息收集')
      expect(wrapper.text()).toContain('信息使用')
      expect(wrapper.text()).toContain('信息保护')
    })
  })

  describe('样式和布局', () => {
    it('勾选框应该有正确的样式', () => {
      const wrapper = createWrapper()
      
      const checkbox = wrapper.find('input[type="checkbox"]')
      expect(checkbox.classes()).toContain('rounded')
      expect(checkbox.classes()).toContain('border-gray-300')
    })

    it('弹窗应该有正确的样式和定位', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      const modal = wrapper.find('[class*="fixed inset-0"]')
      expect(modal.classes()).toContain('fixed')
      expect(modal.classes()).toContain('inset-0')
      expect(modal.classes()).toContain('z-50')
    })

    it('协议文本应该有正确的颜色和字体', () => {
      const wrapper = createWrapper()
      
      const text = wrapper.find('[class*="text-sm text-gray-600"]')
      expect(text.exists()).toBe(true)
    })
  })

  describe('可访问性', () => {
    it('勾选框应该有正确的标签关联', () => {
      const wrapper = createWrapper()
      
      const label = wrapper.find('label')
      const checkbox = wrapper.find('input[type="checkbox"]')
      
      expect(label.exists()).toBe(true)
      expect(checkbox.exists()).toBe(true)
    })

    it('协议链接应该有正确的焦点样式', () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      expect(serviceLink.classes()).toContain('hover:underline')
    })

    it('弹窗按钮应该有正确的焦点样式', async () => {
      const wrapper = createWrapper()
      
      const serviceLink = wrapper.find('button:contains("用户服务协议")')
      await serviceLink.trigger('click')
      
      const agreeButton = wrapper.find('button:contains("同意并继续")')
      expect(agreeButton.exists()).toBe(true)
    })
  })
})
